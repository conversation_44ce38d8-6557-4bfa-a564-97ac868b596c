package com.hnguigu.springcloud.config;

import com.netflix.loadbalancer.IRule;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.cloud.netflix.ribbon.RibbonClient;
import org.springframework.cloud.netflix.ribbon.RibbonClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration
@RibbonClients(
        value = {
                @RibbonClient(name = "goods-service", configuration = RibbonConfig.class)
        }
)
public class RestTemplateConfig {

    @Bean
    @LoadBalanced
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    /**
     * 全局配置
     * @return
     */
    /*@Bean
    public IRule randomRule() {
        return new com.netflix.loadbalancer.RandomRule();
    }*/

}
